# 🍽️ MealPal - AI Food Logging Assistant

MealPal is a Progressive Web App (PWA) that helps you log your daily food intake using AI-powered analysis and automatic Google Sheets integration.

## Features

- 🤖 **AI-Powered Food Analysis**: Uses Google Gemini to parse and analyze your food descriptions
- 📊 **Comprehensive Nutrition Tracking**: Automatically calculates calories, protein, fat, and carbohydrates
- 📈 **Google Sheets Integration**: Automatically logs parsed food data to a reusable spreadsheet in your Google Drive
- 🔐 **Google OAuth Authentication**: Secure login with your Google account
- 📱 **Progressive Web App**: Install on your phone for quick access
- 🎨 **Modern UI**: Clean, responsive design with toast notifications
- ⚡ **Real-time Processing**: Instant food logging with loading states
- 💾 **Smart Spreadsheet Management**: Creates one spreadsheet per user and reuses it for all future entries (persists across server restarts)
- 📅 **Automated Daily Summaries**: Cronjob runs every midnight to calculate and store daily macro totals in a separate "Daily Summary" sheet

## Prerequisites

Before running MealPal, you'll need to set up the following:

### 1. Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the following APIs:
   - Google+ API (for OAuth)
   - Google Sheets API
   - Google Drive API

4. Create OAuth 2.0 credentials:
   - Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
   - Application type: "Web application"
   - Authorized redirect URIs: `http://localhost:8080/auth/google/callback`
   - Note down your Client ID and Client Secret

### 2. Google Gemini API Setup

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key for Gemini
3. Note down your API key

## Installation & Setup

1. **Clone and navigate to the project**:
   ```bash
   cd /path/to/mealpal
   ```

2. **Install dependencies**:
   ```bash
   go mod tidy
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and fill in your actual values:
   ```env
   GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
   GOOGLE_CLIENT_SECRET=your-google-client-secret
   GEMINI_API_KEY=your-gemini-api-key
   SESSION_SECRET=a-long-random-string-for-sessions
   ```

4. **Create PWA icons** (optional but recommended):
   - Add icon files to `static/icons/` directory
   - Use the sizes specified in `static/icons/README.md`
   - You can use online tools like [PWA Builder](https://www.pwabuilder.com/imageGenerator)

5. **Run the application**:
   ```bash
   # Using Makefile (recommended)
   make run

   # Or directly with Go
   go run cmd/main.go
   ```

6. **Access the application**:
   - Open your browser and go to `http://localhost:8080`
   - Click "Continue with Google" to authenticate
   - Start logging your food!
   - After your first food entry, a "View Spreadsheet" link will appear in the user dropdown

## Usage

1. **Login**: Use your Google account to sign in
2. **Log Food**: Describe your meal in natural language, for example:
   - "I had a chicken caesar salad with grilled chicken breast, romaine lettuce, parmesan cheese, and caesar dressing for lunch at 1:30 PM"
   - "Breakfast: 2 scrambled eggs, 2 slices of whole wheat toast with butter, and a cup of coffee"
   - "Snack: 1 medium apple and 10 almonds"

3. **AI Processing**: MealPal will:
   - Parse your description using Google Gemini
   - Extract individual food items, quantities, estimated calories, and macronutrients (protein, fat, carbohydrates)
   - Format the data as CSV with comprehensive nutritional information

4. **Google Sheets**: The parsed data is automatically saved to your personal MealPal spreadsheet in Google Drive (created once and reused for all future entries)

## 📊 Google Sheets Format

Your spreadsheet will have these columns with strict formatting:

| Column | Content | Example |
|--------|---------|---------|
| **Date** | YYYY-MM-DD format | `2025-06-08` |
| **Time** | HH:MM format | `14:30` |
| **Food Item** | Basic food name only | `Rice`, `Chicken breast`, `Apple` |
| **Quantity** | Amount with unit | `1 cup`, `100g`, `1 medium` |
| **Estimated Calories** | Number only | `205`, `165` |
| **Protein (g)** | Number only | `4`, `31` |
| **Fat (g)** | Number only | `0`, `3.6` |
| **Carbohydrates (g)** | Number only | `45`, `0` |
| **Notes** | Cooking methods, brands, etc. | `white, cooked`, `grilled`, `Granny Smith` |

### ✅ **Improved AI Parsing**
- **Strict column enforcement** - No more cooking methods in wrong columns
- **Proper CSV formatting** - Handles quoted fields and special characters
- **Data validation** - Ensures exactly 9 columns per row
- **Error handling** - Skips malformed responses and logs warnings

## 📅 Daily Summary Feature

MealPal automatically generates daily macro summaries using a cronjob that runs every midnight UTC.

### **How It Works**
1. **Automated Processing**: Every midnight, the system processes all users' data from the previous day
2. **Macro Calculation**: Sums up calories, protein, fat, and carbohydrates for each user
3. **Sheet Creation**: Creates a "Daily Summary" sheet in each user's spreadsheet (if it doesn't exist)
4. **Data Storage**: Adds or updates the daily totals in the summary sheet

### **Daily Summary Sheet Format**

| Column | Content | Example |
|--------|---------|---------|
| **Date** | YYYY-MM-DD format | `2025-06-08` |
| **Est. Calories** | Total calories for the day | `2150.5` |
| **Protein** | Total protein in grams | `125.3` |
| **Fat** | Total fat in grams | `85.2` |
| **Carbohydrates** | Total carbs in grams | `245.8` |

### **Features**
- ✅ **Automatic Processing**: No manual intervention required
- ✅ **Error Handling**: Continues processing other users if one fails
- ✅ **Token Refresh**: Automatically refreshes OAuth tokens as needed
- ✅ **Data Persistence**: Updates existing entries if data changes
- ✅ **Graceful Shutdown**: Stops cleanly when server shuts down
- ✅ **Comprehensive Logging**: Detailed logs for monitoring and debugging

### **Manual Trigger** (Development/Testing)
For testing purposes, you can manually trigger the daily summary processing:
```bash
# POST request to trigger daily summary (requires authentication)
curl -X POST http://localhost:8080/api/trigger-daily-summary \
  -H "Cookie: session=your-session-cookie"
```

## Project Structure

```
mealpal/
├── cmd/
│   └── main.go                 # Application entry point
├── internal/
│   ├── config/
│   │   └── config.go          # Configuration management
│   ├── handlers/
│   │   ├── auth.go            # Authentication handlers
│   │   └── app.go             # Application handlers
│   ├── middleware/
│   │   └── auth.go            # Authentication middleware
│   ├── models/
│   │   └── user.go            # Data models
│   └── services/
│       ├── auth.go            # Google OAuth service
│       ├── gemini.go          # Google Gemini API service
│       ├── sheets.go          # Google Sheets API service
│       ├── foodlog.go         # Food logging coordination
│       ├── cronjob.go         # Daily summary cronjob service
│       └── userstore.go       # User data persistence
├── templates/
│   ├── base.html              # Base HTML template
│   ├── home.html              # Login page
│   ├── dashboard.html         # Main app interface
│   └── error.html             # Error page
├── static/
│   ├── css/
│   │   └── style.css          # Application styles
│   ├── js/
│   │   ├── app.js             # Frontend JavaScript
│   │   └── sw.js              # Service Worker for PWA
│   ├── icons/                 # PWA icons
│   └── manifest.json          # PWA manifest
├── .env.example               # Environment variables template
├── go.mod                     # Go module definition
└── README.md                  # This file
```

## Development

### Makefile Commands

The project includes a comprehensive Makefile for easy development and deployment:

```bash
# Show all available commands
make help

# Development
make run              # Run the application in development mode
make dev              # Install dependencies and run in development mode
make setup            # Set up development environment (creates .env from template)

# Building
make build            # Build for current platform
make build-local      # Clean, install deps, and build for current platform
make build-all        # Build for all platforms using goreleaser
make build-linux      # Build for Linux x64 using goreleaser
make deploy-build     # Build for Linux server deployment (simple cross-compile)

# Testing and Quality
make test             # Run tests
make test-coverage    # Run tests with coverage report
make lint             # Format code and run go vet
make clean            # Clean build artifacts

# Debug and Testing
make debug-users      # Show stored user data and spreadsheet info
make reset-data       # Reset all user data (for testing)

# Quick workflows
make quick            # Full development workflow: clean, deps, lint, test, build
```

### Cross-Platform Building

The project uses [GoReleaser](https://goreleaser.com/) for cross-platform builds:

```bash
# Install goreleaser (macOS)
brew install goreleaser/tap/goreleaser

# Build for all platforms
make build-all

# Build specifically for Linux x64 (for server deployment)
make build-linux

# The Linux binary will be at: dist/linux_linux_amd64_v1/mealpal
```

### Running in Development Mode

```bash
# Quick setup and run
make setup    # Creates .env file and installs dependencies
make run      # Starts the development server

# Or manually
go run cmd/main.go
```

### Building for Production

```bash
# For your current platform
make build

# For Linux server (from Mac M1)
make build-linux

# Simple cross-compile for Linux (creates bin/mealpal-linux-amd64)
make deploy-build
```

## Data Persistence & Spreadsheet Management

MealPal provides comprehensive spreadsheet persistence across all scenarios:

### 🆕 **New User Flow**
- ✅ **On signup/login** → Spreadsheet created immediately
- ✅ **On server restart** → User data loaded, same spreadsheet used
- ✅ **On logout/login** → Same spreadsheet reused

### 👤 **Existing User Flow**
- ✅ **On login** → Existing spreadsheet ID loaded and used
- ✅ **On server restart** → User data persisted, same spreadsheet used
- ✅ **On logout/login** → Same spreadsheet always reused

### 🔄 **All Scenarios Covered**
1. **New User Signup** → Spreadsheet created on first login
2. **Existing User Login** → Stored spreadsheet ID retrieved and used
3. **Server Restart** → All user data loaded from `data/users.json`
4. **Logout/Login Cycle** → Spreadsheet persistence maintained

**Data Storage:**
- User data (including spreadsheet IDs) stored in `data/users.json`
- File automatically created and maintained
- Data persists across all server restarts and user sessions
- The `data/` directory is excluded from git for privacy

**Spreadsheet Features:**
- One personal "MealPal Food Log" spreadsheet per user
- Created with proper headers for all nutrition data
- Accessible via "View Spreadsheet" link in user dropdown
- Automatically reused for all future food entries

## Testing Scenarios

To verify all spreadsheet persistence scenarios work correctly:

### 🧪 **Test Plan**

**Scenario 1: New User**
1. Start fresh server (delete `data/` directory if exists)
2. Go to `http://localhost:8080` and login with Google
3. ✅ **Expected**: Spreadsheet created immediately on login
4. Check user dropdown → "View Spreadsheet" link should appear
5. Log some food → should use the same spreadsheet
6. Restart server → login again → should use same spreadsheet

**Scenario 2: Existing User**
1. Login as existing user (who has used the app before)
2. ✅ **Expected**: Existing spreadsheet loaded and used
3. Check `data/users.json` → should contain user with `spreadsheet_id`
4. Log food → should append to existing spreadsheet
5. Logout and login again → should use same spreadsheet

**Scenario 3: Server Restart**
1. With existing users in `data/users.json`
2. Restart server
3. ✅ **Expected**: Console shows "Loaded X users from storage"
4. Login as existing user → should use existing spreadsheet
5. Login as new user → should create new spreadsheet

**Scenario 4: Multiple Users**
1. Login as User A → gets Spreadsheet A
2. Logout, login as User B → gets Spreadsheet B
3. Logout, login as User A again → gets Spreadsheet A (same as before)
4. ✅ **Expected**: Each user has their own persistent spreadsheet

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

If you encounter any issues or have questions, please create an issue in the GitHub repository.
