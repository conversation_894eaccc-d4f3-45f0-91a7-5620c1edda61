package services

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/robfig/cron/v3"
	"github.com/stefanoschrs/mealpal/internal/models"
	"golang.org/x/oauth2"
)

// CronjobService manages scheduled tasks for the application
type CronjobService struct {
	cron         *cron.Cron
	userStore    *UserStore
	sheetsService *SheetsService
	authService  *AuthService
}

// NewCronjobService creates a new cronjob service
func NewCronjobService(userStore *UserStore, sheetsService *SheetsService, authService *AuthService) *CronjobService {
	c := cron.New(cron.WithLocation(time.UTC))
	
	return &CronjobService{
		cron:         c,
		userStore:    userStore,
		sheetsService: sheetsService,
		authService:  authService,
	}
}

// Start begins the cronjob scheduler
func (s *CronjobService) Start() error {
	// Schedule daily macro summary job to run every midnight UTC
	_, err := s.cron.AddFunc("0 0 * * *", s.processDailySummaries)
	if err != nil {
		return fmt.Errorf("failed to schedule daily summary job: %w", err)
	}

	s.cron.Start()
	log.Println("✅ Cronjob service started - Daily macro summaries will run at midnight UTC")
	
	return nil
}

// Stop stops the cronjob scheduler
func (s *CronjobService) Stop() {
	s.cron.Stop()
	log.Println("🛑 Cronjob service stopped")
}

// processDailySummaries processes daily macro summaries for all users
func (s *CronjobService) processDailySummaries() {
	log.Println("🔄 Starting daily macro summary processing...")
	
	// Get yesterday's date in YYYY-MM-DD format
	yesterday := time.Now().UTC().AddDate(0, 0, -1).Format("2006-01-02")
	
	// Get all users
	users := s.userStore.GetAllUsers()
	if len(users) == 0 {
		log.Println("📭 No users found, skipping daily summary processing")
		return
	}
	
	log.Printf("👥 Processing daily summaries for %d users (date: %s)", len(users), yesterday)
	
	successCount := 0
	errorCount := 0
	skippedCount := 0

	for _, user := range users {
		err := s.processUserDailySummary(user, yesterday)
		if err != nil {
			if strings.Contains(err.Error(), "needs to log in again") ||
			   strings.Contains(err.Error(), "no access token") ||
			   strings.Contains(err.Error(), "no refresh token available") {
				log.Printf("⚠️  Skipped user %s: %v", user.Email, err)
				skippedCount++
			} else {
				log.Printf("❌ Failed to process daily summary for user %s: %v", user.Email, err)
				errorCount++
			}
		} else {
			log.Printf("✅ Successfully processed daily summary for user %s", user.Email)
			successCount++
		}
	}
	
	log.Printf("📊 Daily summary processing completed: %d successful, %d errors, %d skipped (need re-login)", successCount, errorCount, skippedCount)
}

// processUserDailySummary processes the daily summary for a single user
func (s *CronjobService) processUserDailySummary(user *models.User, date string) error {
	// Skip users without spreadsheets
	if user.SpreadsheetID == "" {
		return fmt.Errorf("user has no spreadsheet")
	}

	// Skip users without access tokens
	if user.AccessToken == "" {
		return fmt.Errorf("user has no access token (needs to log in again)")
	}

	// Create OAuth token from stored credentials
	token := &oauth2.Token{
		AccessToken:  user.AccessToken,
		RefreshToken: user.RefreshToken,
	}

	// Refresh token if needed
	refreshedToken, err := s.RefreshTokenIfNeeded(token)
	if err != nil {
		// If token refresh fails and there's no refresh token, skip this user
		if user.RefreshToken == "" {
			return fmt.Errorf("user needs to log in again (no refresh token available)")
		}
		return fmt.Errorf("failed to refresh token: %w", err)
	}
	
	// Update user with refreshed token if it changed
	if refreshedToken.AccessToken != token.AccessToken {
		user.AccessToken = refreshedToken.AccessToken
		if refreshedToken.RefreshToken != "" {
			user.RefreshToken = refreshedToken.RefreshToken
		}
		s.userStore.SaveUser(user)
	}
	
	ctx := context.Background()
	
	// Read spreadsheet data for the target date
	data, err := s.sheetsService.ReadSpreadsheetData(ctx, user, refreshedToken, date)
	if err != nil {
		return fmt.Errorf("failed to read spreadsheet data: %w", err)
	}
	
	if len(data) == 0 {
		log.Printf("📭 No data found for user %s on %s, skipping", user.Email, date)
		return nil
	}
	
	// Calculate totals
	totalCalories, totalProtein, totalFat, totalCarbs, err := s.CalculateMacroTotals(data)
	if err != nil {
		return fmt.Errorf("failed to calculate macro totals: %w", err)
	}
	
	// Update Daily Summary sheet
	err = s.sheetsService.CreateOrUpdateDailySummary(ctx, user, refreshedToken, date, totalCalories, totalProtein, totalFat, totalCarbs)
	if err != nil {
		return fmt.Errorf("failed to update daily summary: %w", err)
	}
	
	return nil
}

// RefreshTokenIfNeeded refreshes the OAuth token if it's expired or about to expire
func (s *CronjobService) RefreshTokenIfNeeded(token *oauth2.Token) (*oauth2.Token, error) {
	// Check if token is expired or will expire soon (within 5 minutes)
	if token.Valid() && token.Expiry.After(time.Now().Add(5*time.Minute)) {
		return token, nil
	}
	
	// Refresh the token
	tokenSource := s.authService.GetOAuthConfig().TokenSource(context.Background(), token)
	newToken, err := tokenSource.Token()
	if err != nil {
		return nil, fmt.Errorf("failed to refresh token: %w", err)
	}
	
	return newToken, nil
}

// CalculateMacroTotals calculates the total macros from spreadsheet data
func (s *CronjobService) CalculateMacroTotals(data [][]interface{}) (calories, protein, fat, carbs float64, err error) {
	for _, row := range data {
		if len(row) < 8 {
			continue // Skip rows with insufficient data
		}
		
		// Parse calories (column 4, index 4)
		if caloriesStr, ok := row[4].(string); ok && caloriesStr != "" {
			if cal, parseErr := strconv.ParseFloat(caloriesStr, 64); parseErr == nil {
				calories += cal
			}
		}
		
		// Parse protein (column 5, index 5)
		if proteinStr, ok := row[5].(string); ok && proteinStr != "" {
			if prot, parseErr := strconv.ParseFloat(proteinStr, 64); parseErr == nil {
				protein += prot
			}
		}
		
		// Parse fat (column 6, index 6)
		if fatStr, ok := row[6].(string); ok && fatStr != "" {
			if f, parseErr := strconv.ParseFloat(fatStr, 64); parseErr == nil {
				fat += f
			}
		}
		
		// Parse carbs (column 7, index 7)
		if carbsStr, ok := row[7].(string); ok && carbsStr != "" {
			if c, parseErr := strconv.ParseFloat(carbsStr, 64); parseErr == nil {
				carbs += c
			}
		}
	}
	
	return calories, protein, fat, carbs, nil
}

// RunDailySummaryNow manually triggers the daily summary processing (for testing)
func (s *CronjobService) RunDailySummaryNow() {
	log.Println("🔄 Manually triggering daily summary processing...")
	s.processDailySummaries()
}

// HasValidTokens checks if a user has the necessary tokens for cronjob processing
func (s *CronjobService) HasValidTokens(user *models.User) bool {
	return user.AccessToken != "" && user.RefreshToken != ""
}

// GetUsersNeedingReauth returns a list of users who need to re-authenticate
func (s *CronjobService) GetUsersNeedingReauth() []*models.User {
	allUsers := s.userStore.GetAllUsers()
	var needReauth []*models.User

	for _, user := range allUsers {
		if user.SpreadsheetID != "" && !s.HasValidTokens(user) {
			needReauth = append(needReauth, user)
		}
	}

	return needReauth
}
